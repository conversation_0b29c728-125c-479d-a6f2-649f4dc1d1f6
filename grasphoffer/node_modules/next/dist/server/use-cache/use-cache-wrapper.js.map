{"version": 3, "sources": ["../../../src/server/use-cache/use-cache-wrapper.ts"], "sourcesContent": ["import type { DeepReadonly } from '../../shared/lib/deep-readonly'\n/* eslint-disable import/no-extraneous-dependencies */\nimport {\n  renderToReadableStream,\n  decodeReply,\n  decodeReplyFromAsyncIterable,\n  createTemporaryReferenceSet as createServerTemporaryReferenceSet,\n} from 'react-server-dom-webpack/server.edge'\n/* eslint-disable import/no-extraneous-dependencies */\nimport {\n  createFromReadableStream,\n  encodeReply,\n  createTemporaryReferenceSet as createClientTemporaryReferenceSet,\n} from 'react-server-dom-webpack/client.edge'\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport type {\n  UseCacheStore,\n  WorkUnitStore,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  getHmrRefreshHash,\n  getRenderResumeDataCache,\n  getPrerenderResumeDataCache,\n  workUnitAsyncStorage,\n  getDraftModeProviderForCacheScope,\n} from '../app-render/work-unit-async-storage.external'\nimport { runInCleanSnapshot } from '../app-render/clean-async-snapshot.external'\n\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\n\nimport type { ClientReferenceManifestForRsc } from '../../build/webpack/plugins/flight-manifest-plugin'\n\nimport {\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n} from '../app-render/encryption-utils'\nimport type { CacheEntry } from '../lib/cache-handlers/types'\nimport type { CacheSignal } from '../app-render/cache-signal'\nimport { decryptActionBoundArgs } from '../app-render/encryption'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { getDigestForWellKnownError } from '../app-render/create-error-handler'\nimport { DYNAMIC_EXPIRE } from './constants'\nimport { getCacheHandler } from './handlers'\nimport { UseCacheTimeoutError } from './use-cache-errors'\nimport { createHangingInputAbortSignal } from '../app-render/dynamic-rendering'\nimport {\n  makeErroringExoticSearchParamsForUseCache,\n  type SearchParams,\n} from '../request/search-params'\nimport type { Params } from '../request/params'\nimport React from 'react'\nimport { createLazyResult, isResolvedLazyResult } from '../lib/lazy-result'\n\ntype CacheKeyParts =\n  | [buildId: string, id: string, args: unknown[]]\n  | [buildId: string, id: string, args: unknown[], hmrRefreshHash: string]\n\nexport interface UseCachePageComponentProps {\n  params: Promise<Params>\n  searchParams: Promise<SearchParams>\n  $$isPageComponent: true\n}\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst debug = process.env.NEXT_PRIVATE_DEBUG_CACHE\n  ? console.debug.bind(console, 'use-cache:')\n  : undefined\n\nfunction generateCacheEntry(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n): Promise<[ReadableStream, Promise<CacheEntry>]> {\n  // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n  // generation cannot read anything from the context we're currently executing which\n  // might include request specific things like cookies() inside a React.cache().\n  // Note: It is important that we await at least once before this because it lets us\n  // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n  return runInCleanSnapshot(\n    generateCacheEntryWithRestoredWorkStore,\n    workStore,\n    outerWorkUnitStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    timeoutError\n  )\n}\n\nfunction generateCacheEntryWithRestoredWorkStore(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n) {\n  // Since we cleared the AsyncLocalStorage we need to restore the workStore.\n  // Note: We explicitly don't restore the RequestStore nor the PrerenderStore.\n  // We don't want any request specific information leaking an we don't want to create a\n  // bloated fake request mock for every cache call. So any feature that currently lives\n  // in RequestStore but should be available to Caches need to move to WorkStore.\n  // PrerenderStore is not needed inside the cache scope because the outer most one will\n  // be the one to report its result to the outer Prerender.\n  return workAsyncStorage.run(\n    workStore,\n    generateCacheEntryWithCacheContext,\n    workStore,\n    outerWorkUnitStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    timeoutError\n  )\n}\n\nfunction generateCacheEntryWithCacheContext(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n) {\n  if (!workStore.cacheLifeProfiles) {\n    throw new Error(\n      'cacheLifeProfiles should always be provided. This is a bug in Next.js.'\n    )\n  }\n  const defaultCacheLife = workStore.cacheLifeProfiles['default']\n  if (\n    !defaultCacheLife ||\n    defaultCacheLife.revalidate == null ||\n    defaultCacheLife.expire == null ||\n    defaultCacheLife.stale == null\n  ) {\n    throw new Error(\n      'A default cacheLife profile must always be provided. This is a bug in Next.js.'\n    )\n  }\n\n  const useCacheOrRequestStore =\n    outerWorkUnitStore?.type === 'request' ||\n    outerWorkUnitStore?.type === 'cache'\n      ? outerWorkUnitStore\n      : undefined\n\n  // Initialize the Store for this Cache entry.\n  const cacheStore: UseCacheStore = {\n    type: 'cache',\n    phase: 'render',\n    implicitTags: outerWorkUnitStore?.implicitTags,\n    revalidate: defaultCacheLife.revalidate,\n    expire: defaultCacheLife.expire,\n    stale: defaultCacheLife.stale,\n    explicitRevalidate: undefined,\n    explicitExpire: undefined,\n    explicitStale: undefined,\n    tags: null,\n    hmrRefreshHash:\n      outerWorkUnitStore && getHmrRefreshHash(workStore, outerWorkUnitStore),\n    isHmrRefresh: useCacheOrRequestStore?.isHmrRefresh ?? false,\n    serverComponentsHmrCache: useCacheOrRequestStore?.serverComponentsHmrCache,\n    forceRevalidate: shouldForceRevalidate(workStore, outerWorkUnitStore),\n    draftMode:\n      outerWorkUnitStore &&\n      getDraftModeProviderForCacheScope(workStore, outerWorkUnitStore),\n  }\n\n  return workUnitAsyncStorage.run(\n    cacheStore,\n    generateCacheEntryImpl,\n    workStore,\n    outerWorkUnitStore,\n    cacheStore,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    timeoutError\n  )\n}\n\nfunction propagateCacheLifeAndTags(\n  workUnitStore: WorkUnitStore | undefined,\n  entry: CacheEntry\n): void {\n  if (\n    workUnitStore &&\n    (workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-ppr' ||\n      workUnitStore.type === 'prerender-legacy')\n  ) {\n    // Propagate tags and revalidate upwards\n    const outerTags = workUnitStore.tags ?? (workUnitStore.tags = [])\n    const entryTags = entry.tags\n    for (let i = 0; i < entryTags.length; i++) {\n      const tag = entryTags[i]\n      if (!outerTags.includes(tag)) {\n        outerTags.push(tag)\n      }\n    }\n    if (workUnitStore.stale > entry.stale) {\n      workUnitStore.stale = entry.stale\n    }\n    if (workUnitStore.revalidate > entry.revalidate) {\n      workUnitStore.revalidate = entry.revalidate\n    }\n    if (workUnitStore.expire > entry.expire) {\n      workUnitStore.expire = entry.expire\n    }\n  }\n}\n\nasync function collectResult(\n  savedStream: ReadableStream,\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  innerCacheStore: UseCacheStore,\n  startTime: number,\n  errors: Array<unknown>, // This is a live array that gets pushed into.,\n  timer: any\n): Promise<CacheEntry> {\n  // We create a buffered stream that collects all chunks until the end to\n  // ensure that RSC has finished rendering and therefore we have collected\n  // all tags. In the future the RSC API might allow for the equivalent of\n  // the allReady Promise that exists on SSR streams.\n  //\n  // If something errored or rejected anywhere in the render, we close\n  // the stream as errored. This lets a CacheHandler choose to save the\n  // partial result up until that point for future hits for a while to avoid\n  // unnecessary retries or not to retry. We use the end of the stream for\n  // this to avoid another complicated side-channel. A receiver has to consider\n  // that the stream might also error for other reasons anyway such as losing\n  // connection.\n\n  const buffer: any[] = []\n  const reader = savedStream.getReader()\n  for (let entry; !(entry = await reader.read()).done; ) {\n    buffer.push(entry.value)\n  }\n\n  let idx = 0\n  const bufferStream = new ReadableStream({\n    pull(controller) {\n      if (workStore.invalidUsageError) {\n        controller.error(workStore.invalidUsageError)\n      } else if (idx < buffer.length) {\n        controller.enqueue(buffer[idx++])\n      } else if (errors.length > 0) {\n        // TODO: Should we use AggregateError here?\n        controller.error(errors[0])\n      } else {\n        controller.close()\n      }\n    },\n  })\n\n  const collectedTags = innerCacheStore.tags\n  // If cacheLife() was used to set an explicit revalidate time we use that.\n  // Otherwise, we use the lowest of all inner fetch()/unstable_cache() or nested \"use cache\".\n  // If they're lower than our default.\n  const collectedRevalidate =\n    innerCacheStore.explicitRevalidate !== undefined\n      ? innerCacheStore.explicitRevalidate\n      : innerCacheStore.revalidate\n  const collectedExpire =\n    innerCacheStore.explicitExpire !== undefined\n      ? innerCacheStore.explicitExpire\n      : innerCacheStore.expire\n  const collectedStale =\n    innerCacheStore.explicitStale !== undefined\n      ? innerCacheStore.explicitStale\n      : innerCacheStore.stale\n\n  const entry: CacheEntry = {\n    value: bufferStream,\n    timestamp: startTime,\n    revalidate: collectedRevalidate,\n    expire: collectedExpire,\n    stale: collectedStale,\n    tags: collectedTags === null ? [] : collectedTags,\n  }\n  // Propagate tags/revalidate to the parent context.\n  propagateCacheLifeAndTags(outerWorkUnitStore, entry)\n\n  const cacheSignal =\n    outerWorkUnitStore && outerWorkUnitStore.type === 'prerender'\n      ? outerWorkUnitStore.cacheSignal\n      : null\n  if (cacheSignal) {\n    cacheSignal.endRead()\n  }\n\n  if (timer !== undefined) {\n    clearTimeout(timer)\n  }\n\n  return entry\n}\n\nasync function generateCacheEntryImpl(\n  workStore: WorkStore,\n  outerWorkUnitStore: WorkUnitStore | undefined,\n  innerCacheStore: UseCacheStore,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  timeoutError: UseCacheTimeoutError\n): Promise<[ReadableStream, Promise<CacheEntry>]> {\n  const temporaryReferences = createServerTemporaryReferenceSet()\n\n  const [, , args] =\n    typeof encodedArguments === 'string'\n      ? await decodeReply<CacheKeyParts>(\n          encodedArguments,\n          getServerModuleMap(),\n          { temporaryReferences }\n        )\n      : await decodeReplyFromAsyncIterable<CacheKeyParts>(\n          {\n            async *[Symbol.asyncIterator]() {\n              for (const entry of encodedArguments) {\n                yield entry\n              }\n\n              // The encoded arguments might contain hanging promises. In this\n              // case we don't want to reject with \"Error: Connection closed.\",\n              // so we intentionally keep the iterable alive. This is similar to\n              // the halting trick that we do while rendering.\n              if (outerWorkUnitStore?.type === 'prerender') {\n                await new Promise<void>((resolve) => {\n                  if (outerWorkUnitStore.renderSignal.aborted) {\n                    resolve()\n                  } else {\n                    outerWorkUnitStore.renderSignal.addEventListener(\n                      'abort',\n                      () => resolve(),\n                      { once: true }\n                    )\n                  }\n                })\n              }\n            },\n          },\n          getServerModuleMap(),\n          { temporaryReferences }\n        )\n\n  // Track the timestamp when we started computing the result.\n  const startTime = performance.timeOrigin + performance.now()\n\n  // Invoke the inner function to load a new result. We delay the invocation\n  // though, until React awaits the promise so that React's request store (ALS)\n  // is available when the function is invoked. This allows us, for example, to\n  // capture logs so that we can later replay them.\n  const resultPromise = createLazyResult(() => fn.apply(null, args))\n\n  let errors: Array<unknown> = []\n\n  let timer = undefined\n  const controller = new AbortController()\n  if (outerWorkUnitStore?.type === 'prerender') {\n    // If we're prerendering, we give you 50 seconds to fill a cache entry.\n    // Otherwise we assume you stalled on hanging input and de-opt. This needs\n    // to be lower than just the general timeout of 60 seconds.\n    timer = setTimeout(() => {\n      controller.abort(timeoutError)\n    }, 50000)\n  }\n\n  const stream = renderToReadableStream(\n    resultPromise,\n    clientReferenceManifest.clientModules,\n    {\n      environmentName: 'Cache',\n      signal: controller.signal,\n      temporaryReferences,\n      // In the \"Cache\" environment, we only need to make sure that the error\n      // digests are handled correctly. Error formatting and reporting is not\n      // necessary here; the errors are encoded in the stream, and will be\n      // reported in the \"Server\" environment.\n      onError: (error) => {\n        const digest = getDigestForWellKnownError(error)\n\n        if (digest) {\n          return digest\n        }\n\n        if (process.env.NODE_ENV !== 'development') {\n          // TODO: For now we're also reporting the error here, because in\n          // production, the \"Server\" environment will only get the obfuscated\n          // error (created by the Flight Client in the cache wrapper).\n          console.error(error)\n        }\n\n        if (error === timeoutError) {\n          // The timeout error already aborted the whole stream. We don't need\n          // to also push this error into the `errors` array.\n          return timeoutError.digest\n        }\n\n        errors.push(error)\n      },\n    }\n  )\n\n  const [returnStream, savedStream] = stream.tee()\n\n  const promiseOfCacheEntry = collectResult(\n    savedStream,\n    workStore,\n    outerWorkUnitStore,\n    innerCacheStore,\n    startTime,\n    errors,\n    timer\n  )\n\n  // Return the stream as we're creating it. This means that if it ends up\n  // erroring we cannot return a stale-while-error version but it allows\n  // streaming back the result earlier.\n  return [returnStream, promiseOfCacheEntry]\n}\n\nfunction cloneCacheEntry(entry: CacheEntry): [CacheEntry, CacheEntry] {\n  const [streamA, streamB] = entry.value.tee()\n  entry.value = streamA\n  const clonedEntry: CacheEntry = {\n    value: streamB,\n    timestamp: entry.timestamp,\n    revalidate: entry.revalidate,\n    expire: entry.expire,\n    stale: entry.stale,\n    tags: entry.tags,\n  }\n  return [entry, clonedEntry]\n}\n\nasync function clonePendingCacheEntry(\n  pendingCacheEntry: Promise<CacheEntry>\n): Promise<[CacheEntry, CacheEntry]> {\n  const entry = await pendingCacheEntry\n  return cloneCacheEntry(entry)\n}\n\nasync function getNthCacheEntry(\n  split: Promise<[CacheEntry, CacheEntry]>,\n  i: number\n): Promise<CacheEntry> {\n  return (await split)[i]\n}\n\nasync function encodeFormData(formData: FormData): Promise<string> {\n  let result = ''\n  for (let [key, value] of formData) {\n    // We don't need this key to be serializable but from a security perspective it should not be\n    // possible to generate a string that looks the same from a different structure. To ensure this\n    // we need a delimeter between fields but just using a delimeter is not enough since a string\n    // might contain that delimeter. We use the length of each field as the delimeter to avoid\n    // escaping the values.\n    result += key.length.toString(16) + ':' + key\n    let stringValue\n    if (typeof value === 'string') {\n      stringValue = value\n    } else {\n      // The FormData might contain binary data that is not valid UTF-8 so this cache\n      // key may generate a UCS-2 string. Passing this to another service needs to be\n      // aware that the key might not be compatible.\n      const arrayBuffer = await value.arrayBuffer()\n      if (arrayBuffer.byteLength % 2 === 0) {\n        stringValue = String.fromCodePoint(...new Uint16Array(arrayBuffer))\n      } else {\n        stringValue =\n          String.fromCodePoint(\n            ...new Uint16Array(arrayBuffer, 0, (arrayBuffer.byteLength - 1) / 2)\n          ) +\n          String.fromCodePoint(\n            new Uint8Array(arrayBuffer, arrayBuffer.byteLength - 1, 1)[0]\n          )\n      }\n    }\n    result += stringValue.length.toString(16) + ':' + stringValue\n  }\n  return result\n}\n\nfunction createTrackedReadableStream(\n  stream: ReadableStream,\n  cacheSignal: CacheSignal\n) {\n  const reader = stream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      const { done, value } = await reader.read()\n      if (done) {\n        controller.close()\n        cacheSignal.endRead()\n      } else {\n        controller.enqueue(value)\n      }\n    },\n  })\n}\n\nexport function cache(\n  kind: string,\n  id: string,\n  boundArgsLength: number,\n  originalFn: (...args: unknown[]) => Promise<unknown>\n) {\n  const cacheHandler = getCacheHandler(kind)\n  if (cacheHandler === undefined) {\n    throw new Error('Unknown cache handler: ' + kind)\n  }\n\n  // Capture the timeout error here to ensure a useful stack.\n  const timeoutError = new UseCacheTimeoutError()\n  Error.captureStackTrace(timeoutError, cache)\n\n  const name = originalFn.name\n  const cachedFn = {\n    [name]: async function (...args: any[]) {\n      const workStore = workAsyncStorage.getStore()\n      if (workStore === undefined) {\n        throw new Error(\n          '\"use cache\" cannot be used outside of App Router. Expected a WorkStore.'\n        )\n      }\n\n      let fn = originalFn\n\n      const workUnitStore = workUnitAsyncStorage.getStore()\n\n      // Get the clientReferenceManifest while we're still in the outer Context.\n      // In case getClientReferenceManifestSingleton is implemented using AsyncLocalStorage.\n      const clientReferenceManifest = getClientReferenceManifestForRsc()\n\n      // Because the Action ID is not yet unique per implementation of that Action we can't\n      // safely reuse the results across builds yet. In the meantime we add the buildId to the\n      // arguments as a seed to ensure they're not reused. Remove this once Action IDs hash\n      // the implementation.\n      const buildId = workStore.buildId\n\n      // In dev mode, when the HMR refresh hash is set, we include it in the\n      // cache key. This ensures that cache entries are not reused when server\n      // components have been edited. This is a very coarse approach. But it's\n      // also only a temporary solution until Action IDs are unique per\n      // implementation. Remove this once Action IDs hash the implementation.\n      const hmrRefreshHash =\n        workUnitStore && getHmrRefreshHash(workStore, workUnitStore)\n\n      const hangingInputAbortSignal =\n        workUnitStore?.type === 'prerender'\n          ? createHangingInputAbortSignal(workUnitStore)\n          : undefined\n\n      // When dynamicIO is not enabled, we can not encode searchParams as\n      // hanging promises. To still avoid unused search params from making a\n      // page dynamic, we overwrite them here with a promise that resolves to an\n      // empty object, while also overwriting the to-be-invoked function for\n      // generating a cache entry with a function that creates an erroring\n      // searchParams prop before invoking the original function. This ensures\n      // that used searchParams inside of cached functions would still yield an\n      // error.\n      if (!workStore.dynamicIOEnabled && isPageComponent(args)) {\n        const [{ params, searchParams }] = args\n        // Overwrite the props to omit $$isPageComponent.\n        args = [{ params, searchParams }]\n\n        fn = {\n          [name]: async ({\n            params: serializedParams,\n          }: Omit<UseCachePageComponentProps, '$$isPageComponent'>) =>\n            originalFn.apply(null, [\n              {\n                params: serializedParams,\n                searchParams:\n                  makeErroringExoticSearchParamsForUseCache(workStore),\n              },\n            ]),\n        }[name] as (...args: unknown[]) => Promise<unknown>\n      }\n\n      if (boundArgsLength > 0) {\n        if (args.length === 0) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive its encrypted bound arguments as the first argument.`\n          )\n        }\n\n        const encryptedBoundArgs = args.shift()\n        const boundArgs = await decryptActionBoundArgs(id, encryptedBoundArgs)\n\n        if (!Array.isArray(boundArgs)) {\n          throw new InvariantError(\n            `Expected the bound arguments of \"use cache\" function ${JSON.stringify(fn.name)} to deserialize into an array, got ${typeof boundArgs} instead.`\n          )\n        }\n\n        if (boundArgsLength !== boundArgs.length) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive ${boundArgsLength} bound arguments, got ${boundArgs.length} instead.`\n          )\n        }\n\n        args.unshift(boundArgs)\n      }\n\n      const temporaryReferences = createClientTemporaryReferenceSet()\n\n      const cacheKeyParts: CacheKeyParts = hmrRefreshHash\n        ? [buildId, id, args, hmrRefreshHash]\n        : [buildId, id, args]\n\n      const encodedCacheKeyParts: FormData | string = await encodeReply(\n        cacheKeyParts,\n        { temporaryReferences, signal: hangingInputAbortSignal }\n      )\n\n      const serializedCacheKey =\n        typeof encodedCacheKeyParts === 'string'\n          ? // Fast path for the simple case for simple inputs. We let the CacheHandler\n            // Convert it to an ArrayBuffer if it wants to.\n            encodedCacheKeyParts\n          : await encodeFormData(encodedCacheKeyParts)\n\n      let stream: undefined | ReadableStream = undefined\n\n      // Get an immutable and mutable versions of the resume data cache.\n      const prerenderResumeDataCache = workUnitStore\n        ? getPrerenderResumeDataCache(workUnitStore)\n        : null\n      const renderResumeDataCache = workUnitStore\n        ? getRenderResumeDataCache(workUnitStore)\n        : null\n\n      if (renderResumeDataCache) {\n        const cacheSignal =\n          workUnitStore && workUnitStore.type === 'prerender'\n            ? workUnitStore.cacheSignal\n            : null\n\n        if (cacheSignal) {\n          cacheSignal.beginRead()\n        }\n        const cachedEntry = renderResumeDataCache.cache.get(serializedCacheKey)\n        if (cachedEntry !== undefined) {\n          const existingEntry = await cachedEntry\n          propagateCacheLifeAndTags(workUnitStore, existingEntry)\n          if (\n            workUnitStore !== undefined &&\n            workUnitStore.type === 'prerender' &&\n            existingEntry !== undefined &&\n            (existingEntry.revalidate === 0 ||\n              existingEntry.expire < DYNAMIC_EXPIRE)\n          ) {\n            // In a Dynamic I/O prerender, if the cache entry has revalidate: 0 or if the\n            // expire time is under 5 minutes, then we consider this cache entry dynamic\n            // as it's not worth generating static pages for such data. It's better to leave\n            // a PPR hole that can be filled in dynamically with a potentially cached entry.\n            if (cacheSignal) {\n              cacheSignal.endRead()\n            }\n            return makeHangingPromise(\n              workUnitStore.renderSignal,\n              'dynamic \"use cache\"'\n            )\n          }\n          const [streamA, streamB] = existingEntry.value.tee()\n          existingEntry.value = streamB\n\n          if (cacheSignal) {\n            // When we have a cacheSignal we need to block on reading the cache\n            // entry before ending the read.\n            stream = createTrackedReadableStream(streamA, cacheSignal)\n          } else {\n            stream = streamA\n          }\n        } else {\n          if (cacheSignal) {\n            cacheSignal.endRead()\n          }\n        }\n      }\n\n      if (stream === undefined) {\n        const cacheSignal =\n          workUnitStore && workUnitStore.type === 'prerender'\n            ? workUnitStore.cacheSignal\n            : null\n        if (cacheSignal) {\n          // Either the cache handler or the generation can be using I/O at this point.\n          // We need to track when they start and when they complete.\n          cacheSignal.beginRead()\n        }\n\n        const lazyRefreshTags = workStore.refreshTagsByCacheKind.get(kind)\n\n        if (lazyRefreshTags && !isResolvedLazyResult(lazyRefreshTags)) {\n          await lazyRefreshTags\n        }\n\n        let entry = shouldForceRevalidate(workStore, workUnitStore)\n          ? undefined\n          : 'getExpiration' in cacheHandler\n            ? await cacheHandler.get(serializedCacheKey)\n            : // Legacy cache handlers require implicit tags to be passed in,\n              // instead of checking their staleness here, as we do for modern\n              // cache handlers (see below).\n              await cacheHandler.get(\n                serializedCacheKey,\n                workUnitStore?.implicitTags?.tags ?? []\n              )\n\n        if (entry) {\n          const implicitTags = workUnitStore?.implicitTags?.tags ?? []\n          let implicitTagsExpiration = 0\n\n          if (workUnitStore?.implicitTags) {\n            const lazyExpiration =\n              workUnitStore.implicitTags.expirationsByCacheKind.get(kind)\n\n            if (lazyExpiration) {\n              if (isResolvedLazyResult(lazyExpiration)) {\n                implicitTagsExpiration = lazyExpiration.value\n              } else {\n                implicitTagsExpiration = await lazyExpiration\n              }\n            }\n          }\n\n          if (\n            shouldDiscardCacheEntry(\n              entry,\n              workStore,\n              implicitTags,\n              implicitTagsExpiration\n            )\n          ) {\n            debug?.('discarding stale entry', serializedCacheKey)\n            entry = undefined\n          }\n        }\n\n        const currentTime = performance.timeOrigin + performance.now()\n        if (\n          workUnitStore !== undefined &&\n          workUnitStore.type === 'prerender' &&\n          entry !== undefined &&\n          (entry.revalidate === 0 || entry.expire < DYNAMIC_EXPIRE)\n        ) {\n          // In a Dynamic I/O prerender, if the cache entry has revalidate: 0 or if the\n          // expire time is under 5 minutes, then we consider this cache entry dynamic\n          // as it's not worth generating static pages for such data. It's better to leave\n          // a PPR hole that can be filled in dynamically with a potentially cached entry.\n          if (cacheSignal) {\n            cacheSignal.endRead()\n          }\n\n          return makeHangingPromise(\n            workUnitStore.renderSignal,\n            'dynamic \"use cache\"'\n          )\n        } else if (\n          entry === undefined ||\n          currentTime > entry.timestamp + entry.expire * 1000 ||\n          (workStore.isStaticGeneration &&\n            currentTime > entry.timestamp + entry.revalidate * 1000)\n        ) {\n          // Miss. Generate a new result.\n\n          // If the cache entry is stale and we're prerendering, we don't want to use the\n          // stale entry since it would unnecessarily need to shorten the lifetime of the\n          // prerender. We're not time constrained here so we can re-generated it now.\n\n          // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n          // generation cannot read anything from the context we're currently executing which\n          // might include request specific things like cookies() inside a React.cache().\n          // Note: It is important that we await at least once before this because it lets us\n          // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n\n          if (entry) {\n            if (currentTime > entry.timestamp + entry.expire * 1000) {\n              debug?.('entry is expired', serializedCacheKey)\n            }\n\n            if (\n              workStore.isStaticGeneration &&\n              currentTime > entry.timestamp + entry.revalidate * 1000\n            ) {\n              debug?.('static generation, entry is stale', serializedCacheKey)\n            }\n          }\n\n          const [newStream, pendingCacheEntry] = await generateCacheEntry(\n            workStore,\n            workUnitStore,\n            clientReferenceManifest,\n            encodedCacheKeyParts,\n            fn,\n            timeoutError\n          )\n\n          // When draft mode is enabled, we must not save the cache entry.\n          if (!workStore.isDraftMode) {\n            let savedCacheEntry\n\n            if (prerenderResumeDataCache) {\n              // Create a clone that goes into the cache scope memory cache.\n              const split = clonePendingCacheEntry(pendingCacheEntry)\n              savedCacheEntry = getNthCacheEntry(split, 0)\n              prerenderResumeDataCache.cache.set(\n                serializedCacheKey,\n                getNthCacheEntry(split, 1)\n              )\n            } else {\n              savedCacheEntry = pendingCacheEntry\n            }\n\n            const promise = cacheHandler.set(\n              serializedCacheKey,\n              savedCacheEntry\n            )\n\n            workStore.pendingRevalidateWrites ??= []\n            workStore.pendingRevalidateWrites.push(promise)\n          }\n\n          stream = newStream\n        } else {\n          propagateCacheLifeAndTags(workUnitStore, entry)\n\n          // We want to return this stream, even if it's stale.\n          stream = entry.value\n\n          // If we have a cache scope, we need to clone the entry and set it on\n          // the inner cache scope.\n          if (prerenderResumeDataCache) {\n            const [entryLeft, entryRight] = cloneCacheEntry(entry)\n            if (cacheSignal) {\n              stream = createTrackedReadableStream(entryLeft.value, cacheSignal)\n            } else {\n              stream = entryLeft.value\n            }\n\n            prerenderResumeDataCache.cache.set(\n              serializedCacheKey,\n              Promise.resolve(entryRight)\n            )\n          } else {\n            // If we're not regenerating we need to signal that we've finished\n            // putting the entry into the cache scope at this point. Otherwise we do\n            // that inside generateCacheEntry.\n            cacheSignal?.endRead()\n          }\n\n          if (currentTime > entry.timestamp + entry.revalidate * 1000) {\n            // If this is stale, and we're not in a prerender (i.e. this is dynamic render),\n            // then we should warm up the cache with a fresh revalidated entry.\n            const [ignoredStream, pendingCacheEntry] = await generateCacheEntry(\n              workStore,\n              undefined, // This is not running within the context of this unit.\n              clientReferenceManifest,\n              encodedCacheKeyParts,\n              fn,\n              timeoutError\n            )\n\n            let savedCacheEntry: Promise<CacheEntry>\n            if (prerenderResumeDataCache) {\n              const split = clonePendingCacheEntry(pendingCacheEntry)\n              savedCacheEntry = getNthCacheEntry(split, 0)\n              prerenderResumeDataCache.cache.set(\n                serializedCacheKey,\n                getNthCacheEntry(split, 1)\n              )\n            } else {\n              savedCacheEntry = pendingCacheEntry\n            }\n\n            const promise = cacheHandler.set(\n              serializedCacheKey,\n              savedCacheEntry\n            )\n\n            if (!workStore.pendingRevalidateWrites) {\n              workStore.pendingRevalidateWrites = []\n            }\n            workStore.pendingRevalidateWrites.push(promise)\n\n            await ignoredStream.cancel()\n          }\n        }\n      }\n\n      // Logs are replayed even if it's a hit - to ensure we see them on the client eventually.\n      // If we didn't then the client wouldn't see the logs if it was seeded from a prewarm that\n      // never made it to the client. However, this also means that you see logs even when the\n      // cached function isn't actually re-executed. We should instead ensure prewarms always\n      // make it to the client. Another issue is that this will cause double logging in the\n      // server terminal. Once while generating the cache entry and once when replaying it on\n      // the server, which is required to pick it up for replaying again on the client.\n      const replayConsoleLogs = true\n\n      const serverConsumerManifest = {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n        // which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime\n          ? clientReferenceManifest.edgeRscModuleMapping\n          : clientReferenceManifest.rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      }\n\n      return createFromReadableStream(stream, {\n        serverConsumerManifest,\n        temporaryReferences,\n        replayConsoleLogs,\n        environmentName: 'Cache',\n      })\n    },\n  }[name]\n\n  return React.cache(cachedFn)\n}\n\nfunction isPageComponent(\n  args: any[]\n): args is [UseCachePageComponentProps, undefined] {\n  if (args.length !== 2) {\n    return false\n  }\n\n  const [props, ref] = args\n\n  return (\n    ref === undefined && // server components receive an undefined ref arg\n    props !== null &&\n    typeof props === 'object' &&\n    (props as UseCachePageComponentProps).$$isPageComponent\n  )\n}\n\nfunction shouldForceRevalidate(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore | undefined\n): boolean {\n  if (workStore.isOnDemandRevalidate || workStore.isDraftMode) {\n    return true\n  }\n\n  if (workStore.dev && workUnitStore) {\n    if (workUnitStore.type === 'request') {\n      return workUnitStore.headers.get('cache-control') === 'no-cache'\n    }\n\n    if (workUnitStore.type === 'cache') {\n      return workUnitStore.forceRevalidate\n    }\n  }\n\n  return false\n}\n\nfunction shouldDiscardCacheEntry(\n  entry: CacheEntry,\n  workStore: WorkStore,\n  implicitTags: string[],\n  implicitTagsExpiration: number\n): boolean {\n  // If the cache entry contains revalidated tags that the cache handler might\n  // not know about yet, we need to discard it.\n  if (entry.tags.some((tag) => isRecentlyRevalidatedTag(tag, workStore))) {\n    return true\n  }\n\n  // If the cache entry was created before any of the implicit tags were\n  // revalidated last, we also need to discard it.\n  if (entry.timestamp <= implicitTagsExpiration) {\n    debug?.(\n      'entry was created at',\n      entry.timestamp,\n      'before implicit tags were revalidated at',\n      implicitTagsExpiration\n    )\n\n    return true\n  }\n\n  // Finally, if any of the implicit tags have been revalidated recently, we\n  // also need to discard the cache entry.\n  if (implicitTags.some((tag) => isRecentlyRevalidatedTag(tag, workStore))) {\n    return true\n  }\n\n  return false\n}\n\nfunction isRecentlyRevalidatedTag(tag: string, workStore: WorkStore): boolean {\n  const { previouslyRevalidatedTags, pendingRevalidatedTags } = workStore\n\n  // Was the tag previously revalidated (e.g. by a redirecting server action)?\n  if (previouslyRevalidatedTags.includes(tag)) {\n    debug?.('tag', tag, 'was previously revalidated')\n\n    return true\n  }\n\n  // It could also have been revalidated by the currently running server action.\n  // In this case the revalidation might not have been propagated to the cache\n  // handler yet, so we read it from the pending tags in the work store.\n  if (pendingRevalidatedTags?.includes(tag)) {\n    debug?.('tag', tag, 'was just revalidated')\n\n    return true\n  }\n\n  return false\n}\n"], "names": ["cache", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "debug", "NEXT_PRIVATE_DEBUG_CACHE", "console", "bind", "undefined", "generateCacheEntry", "workStore", "outerWorkUnitStore", "clientReferenceManifest", "encodedArguments", "fn", "timeoutError", "runInCleanSnapshot", "generateCacheEntryWithRestoredWorkStore", "workAsyncStorage", "run", "generateCacheEntryWithCacheContext", "cacheLifeProfiles", "Error", "defaultCacheLife", "revalidate", "expire", "stale", "useCacheOrRequestStore", "type", "cacheStore", "phase", "implicitTags", "explicitRevalidate", "explicitExpire", "explicitStale", "tags", "hmrRefreshHash", "getHmrRefreshHash", "isHmrRefresh", "serverComponentsHmrCache", "forceRevalidate", "shouldForceRevalidate", "draftMode", "getDraftModeProviderForCacheScope", "workUnitAsyncStorage", "generateCacheEntryImpl", "propagateCacheLifeAndTags", "workUnitStore", "entry", "outerTags", "entryTags", "i", "length", "tag", "includes", "push", "collectResult", "savedStream", "innerCacheStore", "startTime", "errors", "timer", "buffer", "reader", "<PERSON><PERSON><PERSON><PERSON>", "read", "done", "value", "idx", "bufferStream", "ReadableStream", "pull", "controller", "invalidUsageError", "error", "enqueue", "close", "collectedTags", "collectedRevalidate", "collectedExpire", "collectedStale", "timestamp", "cacheSignal", "endRead", "clearTimeout", "temporaryReferences", "createServerTemporaryReferenceSet", "args", "decodeReply", "getServerModuleMap", "decodeReplyFromAsyncIterable", "Symbol", "asyncIterator", "Promise", "resolve", "renderSignal", "aborted", "addEventListener", "once", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "resultPromise", "createLazyResult", "apply", "AbortController", "setTimeout", "abort", "stream", "renderToReadableStream", "clientModules", "environmentName", "signal", "onError", "digest", "getDigestForWellKnownError", "NODE_ENV", "returnStream", "tee", "promiseOfCacheEntry", "cloneCacheEntry", "streamA", "streamB", "clonedEntry", "clonePendingCacheEntry", "pendingCacheEntry", "getNthCacheEntry", "split", "encodeFormData", "formData", "result", "key", "toString", "stringValue", "arrayBuffer", "byteLength", "String", "fromCodePoint", "Uint16Array", "Uint8Array", "createTrackedReadableStream", "kind", "id", "boundArgs<PERSON><PERSON>th", "originalFn", "cache<PERSON><PERSON><PERSON>", "getCache<PERSON><PERSON><PERSON>", "UseCacheTimeoutError", "captureStackTrace", "name", "cachedFn", "getStore", "getClientReferenceManifestForRsc", "buildId", "hangingInputAbortSignal", "createHangingInputAbortSignal", "dynamicIOEnabled", "isPageComponent", "params", "searchParams", "serializedParams", "makeErroringExoticSearchParamsForUseCache", "InvariantError", "JSON", "stringify", "encryptedBoundArgs", "shift", "boundArgs", "decryptActionBoundArgs", "Array", "isArray", "unshift", "createClientTemporaryReferenceSet", "cacheKeyParts", "encodedCacheKeyParts", "encodeReply", "serialized<PERSON>ache<PERSON>ey", "prerenderResumeDataCache", "getPrerenderResumeDataCache", "renderResumeDataCache", "getRenderResumeDataCache", "beginRead", "cachedEntry", "get", "existingEntry", "DYNAMIC_EXPIRE", "makeHangingPromise", "lazyRefreshTags", "refreshTagsByCacheKind", "isResolvedLazyResult", "implicitTagsExpiration", "lazyExpiration", "expirationsByCacheKind", "shouldDiscardCacheEntry", "currentTime", "isStaticGeneration", "newStream", "isDraftMode", "savedCacheEntry", "set", "promise", "pendingRevalidateWrites", "entryLeft", "entryRight", "ignoredStream", "cancel", "replayConsoleLogs", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeRscModuleMapping", "rscModuleMapping", "serverModuleMap", "createFromReadableStream", "React", "props", "ref", "$$isPageComponent", "isOnDemandRevalidate", "dev", "headers", "some", "isRecentlyRevalidatedTag", "previouslyRevalidatedTags", "pendingRevalidatedTags"], "mappings": ";;;;+BA+fgBA;;;eAAAA;;;4BAxfT;4BAMA;0CAG0B;8CAW1B;4CAC4B;uCAEA;iCAO5B;4BAGgC;gCACR;oCACY;2BACZ;0BACC;gCACK;kCACS;8BAIvC;8DAEW;4BACqC;;;;;;AAYvD,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,QAAQH,QAAQC,GAAG,CAACG,wBAAwB,GAC9CC,QAAQF,KAAK,CAACG,IAAI,CAACD,SAAS,gBAC5BE;AAEJ,SAASC,mBACPC,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,kFAAkF;IAClF,mFAAmF;IACnF,+EAA+E;IAC/E,mFAAmF;IACnF,6EAA6E;IAC7E,OAAOC,IAAAA,8CAAkB,EACvBC,yCACAP,WACAC,oBACAC,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAASE,wCACPP,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,2EAA2E;IAC3E,6EAA6E;IAC7E,sFAAsF;IACtF,sFAAsF;IACtF,+EAA+E;IAC/E,sFAAsF;IACtF,0DAA0D;IAC1D,OAAOG,0CAAgB,CAACC,GAAG,CACzBT,WACAU,oCACAV,WACAC,oBACAC,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAASK,mCACPV,SAAoB,EACpBC,kBAA6C,EAC7CC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,IAAI,CAACL,UAAUW,iBAAiB,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAIC,MACR,2EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,MAAMC,mBAAmBb,UAAUW,iBAAiB,CAAC,UAAU;IAC/D,IACE,CAACE,oBACDA,iBAAiBC,UAAU,IAAI,QAC/BD,iBAAiBE,MAAM,IAAI,QAC3BF,iBAAiBG,KAAK,IAAI,MAC1B;QACA,MAAM,qBAEL,CAFK,IAAIJ,MACR,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMK,yBACJhB,CAAAA,sCAAAA,mBAAoBiB,IAAI,MAAK,aAC7BjB,CAAAA,sCAAAA,mBAAoBiB,IAAI,MAAK,UACzBjB,qBACAH;IAEN,6CAA6C;IAC7C,MAAMqB,aAA4B;QAChCD,MAAM;QACNE,OAAO;QACPC,YAAY,EAAEpB,sCAAAA,mBAAoBoB,YAAY;QAC9CP,YAAYD,iBAAiBC,UAAU;QACvCC,QAAQF,iBAAiBE,MAAM;QAC/BC,OAAOH,iBAAiBG,KAAK;QAC7BM,oBAAoBxB;QACpByB,gBAAgBzB;QAChB0B,eAAe1B;QACf2B,MAAM;QACNC,gBACEzB,sBAAsB0B,IAAAA,+CAAiB,EAAC3B,WAAWC;QACrD2B,cAAcX,CAAAA,0CAAAA,uBAAwBW,YAAY,KAAI;QACtDC,wBAAwB,EAAEZ,0CAAAA,uBAAwBY,wBAAwB;QAC1EC,iBAAiBC,sBAAsB/B,WAAWC;QAClD+B,WACE/B,sBACAgC,IAAAA,+DAAiC,EAACjC,WAAWC;IACjD;IAEA,OAAOiC,kDAAoB,CAACzB,GAAG,CAC7BU,YACAgB,wBACAnC,WACAC,oBACAkB,YACAjB,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAAS+B,0BACPC,aAAwC,EACxCC,KAAiB;IAEjB,IACED,iBACCA,CAAAA,cAAcnB,IAAI,KAAK,WACtBmB,cAAcnB,IAAI,KAAK,eACvBmB,cAAcnB,IAAI,KAAK,mBACvBmB,cAAcnB,IAAI,KAAK,kBAAiB,GAC1C;QACA,wCAAwC;QACxC,MAAMqB,YAAYF,cAAcZ,IAAI,IAAKY,CAAAA,cAAcZ,IAAI,GAAG,EAAE,AAAD;QAC/D,MAAMe,YAAYF,MAAMb,IAAI;QAC5B,IAAK,IAAIgB,IAAI,GAAGA,IAAID,UAAUE,MAAM,EAAED,IAAK;YACzC,MAAME,MAAMH,SAAS,CAACC,EAAE;YACxB,IAAI,CAACF,UAAUK,QAAQ,CAACD,MAAM;gBAC5BJ,UAAUM,IAAI,CAACF;YACjB;QACF;QACA,IAAIN,cAAcrB,KAAK,GAAGsB,MAAMtB,KAAK,EAAE;YACrCqB,cAAcrB,KAAK,GAAGsB,MAAMtB,KAAK;QACnC;QACA,IAAIqB,cAAcvB,UAAU,GAAGwB,MAAMxB,UAAU,EAAE;YAC/CuB,cAAcvB,UAAU,GAAGwB,MAAMxB,UAAU;QAC7C;QACA,IAAIuB,cAActB,MAAM,GAAGuB,MAAMvB,MAAM,EAAE;YACvCsB,cAActB,MAAM,GAAGuB,MAAMvB,MAAM;QACrC;IACF;AACF;AAEA,eAAe+B,cACbC,WAA2B,EAC3B/C,SAAoB,EACpBC,kBAA6C,EAC7C+C,eAA8B,EAC9BC,SAAiB,EACjBC,MAAsB,EACtBC,KAAU;IAEV,wEAAwE;IACxE,yEAAyE;IACzE,wEAAwE;IACxE,mDAAmD;IACnD,EAAE;IACF,oEAAoE;IACpE,qEAAqE;IACrE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,2EAA2E;IAC3E,cAAc;IAEd,MAAMC,SAAgB,EAAE;IACxB,MAAMC,SAASN,YAAYO,SAAS;IACpC,IAAK,IAAIhB,OAAO,CAAC,AAACA,CAAAA,QAAQ,MAAMe,OAAOE,IAAI,EAAC,EAAGC,IAAI,EAAI;QACrDJ,OAAOP,IAAI,CAACP,MAAMmB,KAAK;IACzB;IAEA,IAAIC,MAAM;IACV,MAAMC,eAAe,IAAIC,eAAe;QACtCC,MAAKC,UAAU;YACb,IAAI9D,UAAU+D,iBAAiB,EAAE;gBAC/BD,WAAWE,KAAK,CAAChE,UAAU+D,iBAAiB;YAC9C,OAAO,IAAIL,MAAMN,OAAOV,MAAM,EAAE;gBAC9BoB,WAAWG,OAAO,CAACb,MAAM,CAACM,MAAM;YAClC,OAAO,IAAIR,OAAOR,MAAM,GAAG,GAAG;gBAC5B,2CAA2C;gBAC3CoB,WAAWE,KAAK,CAACd,MAAM,CAAC,EAAE;YAC5B,OAAO;gBACLY,WAAWI,KAAK;YAClB;QACF;IACF;IAEA,MAAMC,gBAAgBnB,gBAAgBvB,IAAI;IAC1C,0EAA0E;IAC1E,4FAA4F;IAC5F,qCAAqC;IACrC,MAAM2C,sBACJpB,gBAAgB1B,kBAAkB,KAAKxB,YACnCkD,gBAAgB1B,kBAAkB,GAClC0B,gBAAgBlC,UAAU;IAChC,MAAMuD,kBACJrB,gBAAgBzB,cAAc,KAAKzB,YAC/BkD,gBAAgBzB,cAAc,GAC9ByB,gBAAgBjC,MAAM;IAC5B,MAAMuD,iBACJtB,gBAAgBxB,aAAa,KAAK1B,YAC9BkD,gBAAgBxB,aAAa,GAC7BwB,gBAAgBhC,KAAK;IAE3B,MAAMsB,QAAoB;QACxBmB,OAAOE;QACPY,WAAWtB;QACXnC,YAAYsD;QACZrD,QAAQsD;QACRrD,OAAOsD;QACP7C,MAAM0C,kBAAkB,OAAO,EAAE,GAAGA;IACtC;IACA,mDAAmD;IACnD/B,0BAA0BnC,oBAAoBqC;IAE9C,MAAMkC,cACJvE,sBAAsBA,mBAAmBiB,IAAI,KAAK,cAC9CjB,mBAAmBuE,WAAW,GAC9B;IACN,IAAIA,aAAa;QACfA,YAAYC,OAAO;IACrB;IAEA,IAAItB,UAAUrD,WAAW;QACvB4E,aAAavB;IACf;IAEA,OAAOb;AACT;AAEA,eAAeH,uBACbnC,SAAoB,EACpBC,kBAA6C,EAC7C+C,eAA8B,EAC9B9C,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,YAAkC;IAElC,MAAMsE,sBAAsBC,IAAAA,uCAAiC;IAE7D,MAAM,KAAKC,KAAK,GACd,OAAO1E,qBAAqB,WACxB,MAAM2E,IAAAA,uBAAW,EACf3E,kBACA4E,IAAAA,mCAAkB,KAClB;QAAEJ;IAAoB,KAExB,MAAMK,IAAAA,wCAA4B,EAChC;QACE,OAAO,CAACC,OAAOC,aAAa,CAAC;YAC3B,KAAK,MAAM5C,SAASnC,iBAAkB;gBACpC,MAAMmC;YACR;YAEA,gEAAgE;YAChE,iEAAiE;YACjE,kEAAkE;YAClE,gDAAgD;YAChD,IAAIrC,CAAAA,sCAAAA,mBAAoBiB,IAAI,MAAK,aAAa;gBAC5C,MAAM,IAAIiE,QAAc,CAACC;oBACvB,IAAInF,mBAAmBoF,YAAY,CAACC,OAAO,EAAE;wBAC3CF;oBACF,OAAO;wBACLnF,mBAAmBoF,YAAY,CAACE,gBAAgB,CAC9C,SACA,IAAMH,WACN;4BAAEI,MAAM;wBAAK;oBAEjB;gBACF;YACF;QACF;IACF,GACAT,IAAAA,mCAAkB,KAClB;QAAEJ;IAAoB;IAG9B,4DAA4D;IAC5D,MAAM1B,YAAYwC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;IAE1D,0EAA0E;IAC1E,6EAA6E;IAC7E,6EAA6E;IAC7E,iDAAiD;IACjD,MAAMC,gBAAgBC,IAAAA,4BAAgB,EAAC,IAAMzF,GAAG0F,KAAK,CAAC,MAAMjB;IAE5D,IAAI3B,SAAyB,EAAE;IAE/B,IAAIC,QAAQrD;IACZ,MAAMgE,aAAa,IAAIiC;IACvB,IAAI9F,CAAAA,sCAAAA,mBAAoBiB,IAAI,MAAK,aAAa;QAC5C,uEAAuE;QACvE,0EAA0E;QAC1E,2DAA2D;QAC3DiC,QAAQ6C,WAAW;YACjBlC,WAAWmC,KAAK,CAAC5F;QACnB,GAAG;IACL;IAEA,MAAM6F,SAASC,IAAAA,kCAAsB,EACnCP,eACA1F,wBAAwBkG,aAAa,EACrC;QACEC,iBAAiB;QACjBC,QAAQxC,WAAWwC,MAAM;QACzB3B;QACA,uEAAuE;QACvE,uEAAuE;QACvE,oEAAoE;QACpE,wCAAwC;QACxC4B,SAAS,CAACvC;YACR,MAAMwC,SAASC,IAAAA,8CAA0B,EAACzC;YAE1C,IAAIwC,QAAQ;gBACV,OAAOA;YACT;YAEA,IAAIjH,QAAQC,GAAG,CAACkH,QAAQ,KAAK,eAAe;gBAC1C,gEAAgE;gBAChE,oEAAoE;gBACpE,6DAA6D;gBAC7D9G,QAAQoE,KAAK,CAACA;YAChB;YAEA,IAAIA,UAAU3D,cAAc;gBAC1B,oEAAoE;gBACpE,mDAAmD;gBACnD,OAAOA,aAAamG,MAAM;YAC5B;YAEAtD,OAAOL,IAAI,CAACmB;QACd;IACF;IAGF,MAAM,CAAC2C,cAAc5D,YAAY,GAAGmD,OAAOU,GAAG;IAE9C,MAAMC,sBAAsB/D,cAC1BC,aACA/C,WACAC,oBACA+C,iBACAC,WACAC,QACAC;IAGF,wEAAwE;IACxE,sEAAsE;IACtE,qCAAqC;IACrC,OAAO;QAACwD;QAAcE;KAAoB;AAC5C;AAEA,SAASC,gBAAgBxE,KAAiB;IACxC,MAAM,CAACyE,SAASC,QAAQ,GAAG1E,MAAMmB,KAAK,CAACmD,GAAG;IAC1CtE,MAAMmB,KAAK,GAAGsD;IACd,MAAME,cAA0B;QAC9BxD,OAAOuD;QACPzC,WAAWjC,MAAMiC,SAAS;QAC1BzD,YAAYwB,MAAMxB,UAAU;QAC5BC,QAAQuB,MAAMvB,MAAM;QACpBC,OAAOsB,MAAMtB,KAAK;QAClBS,MAAMa,MAAMb,IAAI;IAClB;IACA,OAAO;QAACa;QAAO2E;KAAY;AAC7B;AAEA,eAAeC,uBACbC,iBAAsC;IAEtC,MAAM7E,QAAQ,MAAM6E;IACpB,OAAOL,gBAAgBxE;AACzB;AAEA,eAAe8E,iBACbC,KAAwC,EACxC5E,CAAS;IAET,OAAO,AAAC,CAAA,MAAM4E,KAAI,CAAE,CAAC5E,EAAE;AACzB;AAEA,eAAe6E,eAAeC,QAAkB;IAC9C,IAAIC,SAAS;IACb,KAAK,IAAI,CAACC,KAAKhE,MAAM,IAAI8D,SAAU;QACjC,6FAA6F;QAC7F,+FAA+F;QAC/F,6FAA6F;QAC7F,0FAA0F;QAC1F,uBAAuB;QACvBC,UAAUC,IAAI/E,MAAM,CAACgF,QAAQ,CAAC,MAAM,MAAMD;QAC1C,IAAIE;QACJ,IAAI,OAAOlE,UAAU,UAAU;YAC7BkE,cAAclE;QAChB,OAAO;YACL,+EAA+E;YAC/E,+EAA+E;YAC/E,8CAA8C;YAC9C,MAAMmE,cAAc,MAAMnE,MAAMmE,WAAW;YAC3C,IAAIA,YAAYC,UAAU,GAAG,MAAM,GAAG;gBACpCF,cAAcG,OAAOC,aAAa,IAAI,IAAIC,YAAYJ;YACxD,OAAO;gBACLD,cACEG,OAAOC,aAAa,IACf,IAAIC,YAAYJ,aAAa,GAAG,AAACA,CAAAA,YAAYC,UAAU,GAAG,CAAA,IAAK,MAEpEC,OAAOC,aAAa,CAClB,IAAIE,WAAWL,aAAaA,YAAYC,UAAU,GAAG,GAAG,EAAE,CAAC,EAAE;YAEnE;QACF;QACAL,UAAUG,YAAYjF,MAAM,CAACgF,QAAQ,CAAC,MAAM,MAAMC;IACpD;IACA,OAAOH;AACT;AAEA,SAASU,4BACPhC,MAAsB,EACtB1B,WAAwB;IAExB,MAAMnB,SAAS6C,OAAO5C,SAAS;IAC/B,OAAO,IAAIM,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAM,EAAEN,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOE,IAAI;YACzC,IAAIC,MAAM;gBACRM,WAAWI,KAAK;gBAChBM,YAAYC,OAAO;YACrB,OAAO;gBACLX,WAAWG,OAAO,CAACR;YACrB;QACF;IACF;AACF;AAEO,SAASpE,MACd8I,IAAY,EACZC,EAAU,EACVC,eAAuB,EACvBC,UAAoD;IAEpD,MAAMC,eAAeC,IAAAA,yBAAe,EAACL;IACrC,IAAII,iBAAiBzI,WAAW;QAC9B,MAAM,qBAA2C,CAA3C,IAAIc,MAAM,4BAA4BuH,OAAtC,qBAAA;mBAAA;wBAAA;0BAAA;QAA0C;IAClD;IAEA,2DAA2D;IAC3D,MAAM9H,eAAe,IAAIoI,oCAAoB;IAC7C7H,MAAM8H,iBAAiB,CAACrI,cAAchB;IAEtC,MAAMsJ,OAAOL,WAAWK,IAAI;IAC5B,MAAMC,WAAW;QACf,CAACD,KAAK,EAAE,eAAgB,GAAG9D,IAAW;YACpC,MAAM7E,YAAYQ,0CAAgB,CAACqI,QAAQ;YAC3C,IAAI7I,cAAcF,WAAW;gBAC3B,MAAM,qBAEL,CAFK,IAAIc,MACR,4EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIR,KAAKkI;YAET,MAAMjG,gBAAgBH,kDAAoB,CAAC2G,QAAQ;YAEnD,0EAA0E;YAC1E,sFAAsF;YACtF,MAAM3I,0BAA0B4I,IAAAA,iDAAgC;YAEhE,qFAAqF;YACrF,wFAAwF;YACxF,qFAAqF;YACrF,sBAAsB;YACtB,MAAMC,UAAU/I,UAAU+I,OAAO;YAEjC,sEAAsE;YACtE,wEAAwE;YACxE,wEAAwE;YACxE,iEAAiE;YACjE,uEAAuE;YACvE,MAAMrH,iBACJW,iBAAiBV,IAAAA,+CAAiB,EAAC3B,WAAWqC;YAEhD,MAAM2G,0BACJ3G,CAAAA,iCAAAA,cAAenB,IAAI,MAAK,cACpB+H,IAAAA,+CAA6B,EAAC5G,iBAC9BvC;YAEN,mEAAmE;YACnE,sEAAsE;YACtE,0EAA0E;YAC1E,sEAAsE;YACtE,oEAAoE;YACpE,wEAAwE;YACxE,yEAAyE;YACzE,SAAS;YACT,IAAI,CAACE,UAAUkJ,gBAAgB,IAAIC,gBAAgBtE,OAAO;gBACxD,MAAM,CAAC,EAAEuE,MAAM,EAAEC,YAAY,EAAE,CAAC,GAAGxE;gBACnC,iDAAiD;gBACjDA,OAAO;oBAAC;wBAAEuE;wBAAQC;oBAAa;iBAAE;gBAEjCjJ,KAAK,CAAA;oBACH,CAACuI,KAAK,EAAE,OAAO,EACbS,QAAQE,gBAAgB,EAC8B,GACtDhB,WAAWxC,KAAK,CAAC,MAAM;4BACrB;gCACEsD,QAAQE;gCACRD,cACEE,IAAAA,uDAAyC,EAACvJ;4BAC9C;yBACD;gBACL,CAAA,CAAC,CAAC2I,KAAK;YACT;YAEA,IAAIN,kBAAkB,GAAG;gBACvB,IAAIxD,KAAKnC,MAAM,KAAK,GAAG;oBACrB,MAAM,qBAEL,CAFK,IAAI8G,8BAAc,CACtB,CAAC,kCAAkC,EAAEC,KAAKC,SAAS,CAACtJ,GAAGuI,IAAI,EAAE,gEAAgE,CAAC,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,MAAMgB,qBAAqB9E,KAAK+E,KAAK;gBACrC,MAAMC,YAAY,MAAMC,IAAAA,kCAAsB,EAAC1B,IAAIuB;gBAEnD,IAAI,CAACI,MAAMC,OAAO,CAACH,YAAY;oBAC7B,MAAM,qBAEL,CAFK,IAAIL,8BAAc,CACtB,CAAC,qDAAqD,EAAEC,KAAKC,SAAS,CAACtJ,GAAGuI,IAAI,EAAE,mCAAmC,EAAE,OAAOkB,UAAU,SAAS,CAAC,GAD5I,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIxB,oBAAoBwB,UAAUnH,MAAM,EAAE;oBACxC,MAAM,qBAEL,CAFK,IAAI8G,8BAAc,CACtB,CAAC,kCAAkC,EAAEC,KAAKC,SAAS,CAACtJ,GAAGuI,IAAI,EAAE,YAAY,EAAEN,gBAAgB,sBAAsB,EAAEwB,UAAUnH,MAAM,CAAC,SAAS,CAAC,GAD1I,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEAmC,KAAKoF,OAAO,CAACJ;YACf;YAEA,MAAMlF,sBAAsBuF,IAAAA,uCAAiC;YAE7D,MAAMC,gBAA+BzI,iBACjC;gBAACqH;gBAASX;gBAAIvD;gBAAMnD;aAAe,GACnC;gBAACqH;gBAASX;gBAAIvD;aAAK;YAEvB,MAAMuF,uBAA0C,MAAMC,IAAAA,uBAAW,EAC/DF,eACA;gBAAExF;gBAAqB2B,QAAQ0C;YAAwB;YAGzD,MAAMsB,qBACJ,OAAOF,yBAAyB,WAE5B,+CAA+C;YAC/CA,uBACA,MAAM9C,eAAe8C;YAE3B,IAAIlE,SAAqCpG;YAEzC,kEAAkE;YAClE,MAAMyK,2BAA2BlI,gBAC7BmI,IAAAA,yDAA2B,EAACnI,iBAC5B;YACJ,MAAMoI,wBAAwBpI,gBAC1BqI,IAAAA,sDAAwB,EAACrI,iBACzB;YAEJ,IAAIoI,uBAAuB;gBACzB,MAAMjG,cACJnC,iBAAiBA,cAAcnB,IAAI,KAAK,cACpCmB,cAAcmC,WAAW,GACzB;gBAEN,IAAIA,aAAa;oBACfA,YAAYmG,SAAS;gBACvB;gBACA,MAAMC,cAAcH,sBAAsBpL,KAAK,CAACwL,GAAG,CAACP;gBACpD,IAAIM,gBAAgB9K,WAAW;oBAC7B,MAAMgL,gBAAgB,MAAMF;oBAC5BxI,0BAA0BC,eAAeyI;oBACzC,IACEzI,kBAAkBvC,aAClBuC,cAAcnB,IAAI,KAAK,eACvB4J,kBAAkBhL,aACjBgL,CAAAA,cAAchK,UAAU,KAAK,KAC5BgK,cAAc/J,MAAM,GAAGgK,yBAAc,AAAD,GACtC;wBACA,6EAA6E;wBAC7E,4EAA4E;wBAC5E,gFAAgF;wBAChF,gFAAgF;wBAChF,IAAIvG,aAAa;4BACfA,YAAYC,OAAO;wBACrB;wBACA,OAAOuG,IAAAA,yCAAkB,EACvB3I,cAAcgD,YAAY,EAC1B;oBAEJ;oBACA,MAAM,CAAC0B,SAASC,QAAQ,GAAG8D,cAAcrH,KAAK,CAACmD,GAAG;oBAClDkE,cAAcrH,KAAK,GAAGuD;oBAEtB,IAAIxC,aAAa;wBACf,mEAAmE;wBACnE,gCAAgC;wBAChC0B,SAASgC,4BAA4BnB,SAASvC;oBAChD,OAAO;wBACL0B,SAASa;oBACX;gBACF,OAAO;oBACL,IAAIvC,aAAa;wBACfA,YAAYC,OAAO;oBACrB;gBACF;YACF;YAEA,IAAIyB,WAAWpG,WAAW;oBA0BhBuC;gBAzBR,MAAMmC,cACJnC,iBAAiBA,cAAcnB,IAAI,KAAK,cACpCmB,cAAcmC,WAAW,GACzB;gBACN,IAAIA,aAAa;oBACf,6EAA6E;oBAC7E,2DAA2D;oBAC3DA,YAAYmG,SAAS;gBACvB;gBAEA,MAAMM,kBAAkBjL,UAAUkL,sBAAsB,CAACL,GAAG,CAAC1C;gBAE7D,IAAI8C,mBAAmB,CAACE,IAAAA,gCAAoB,EAACF,kBAAkB;oBAC7D,MAAMA;gBACR;gBAEA,IAAI3I,QAAQP,sBAAsB/B,WAAWqC,iBACzCvC,YACA,mBAAmByI,eACjB,MAAMA,aAAasC,GAAG,CAACP,sBAEvB,gEAAgE;gBAChE,8BAA8B;gBAC9B,MAAM/B,aAAasC,GAAG,CACpBP,oBACAjI,CAAAA,kCAAAA,8BAAAA,cAAehB,YAAY,qBAA3BgB,4BAA6BZ,IAAI,KAAI,EAAE;gBAG/C,IAAIa,OAAO;wBACYD;oBAArB,MAAMhB,eAAegB,CAAAA,kCAAAA,+BAAAA,cAAehB,YAAY,qBAA3BgB,6BAA6BZ,IAAI,KAAI,EAAE;oBAC5D,IAAI2J,yBAAyB;oBAE7B,IAAI/I,iCAAAA,cAAehB,YAAY,EAAE;wBAC/B,MAAMgK,iBACJhJ,cAAchB,YAAY,CAACiK,sBAAsB,CAACT,GAAG,CAAC1C;wBAExD,IAAIkD,gBAAgB;4BAClB,IAAIF,IAAAA,gCAAoB,EAACE,iBAAiB;gCACxCD,yBAAyBC,eAAe5H,KAAK;4BAC/C,OAAO;gCACL2H,yBAAyB,MAAMC;4BACjC;wBACF;oBACF;oBAEA,IACEE,wBACEjJ,OACAtC,WACAqB,cACA+J,yBAEF;wBACA1L,yBAAAA,MAAQ,0BAA0B4K;wBAClChI,QAAQxC;oBACV;gBACF;gBAEA,MAAM0L,cAAc/F,YAAYC,UAAU,GAAGD,YAAYE,GAAG;gBAC5D,IACEtD,kBAAkBvC,aAClBuC,cAAcnB,IAAI,KAAK,eACvBoB,UAAUxC,aACTwC,CAAAA,MAAMxB,UAAU,KAAK,KAAKwB,MAAMvB,MAAM,GAAGgK,yBAAc,AAAD,GACvD;oBACA,6EAA6E;oBAC7E,4EAA4E;oBAC5E,gFAAgF;oBAChF,gFAAgF;oBAChF,IAAIvG,aAAa;wBACfA,YAAYC,OAAO;oBACrB;oBAEA,OAAOuG,IAAAA,yCAAkB,EACvB3I,cAAcgD,YAAY,EAC1B;gBAEJ,OAAO,IACL/C,UAAUxC,aACV0L,cAAclJ,MAAMiC,SAAS,GAAGjC,MAAMvB,MAAM,GAAG,QAC9Cf,UAAUyL,kBAAkB,IAC3BD,cAAclJ,MAAMiC,SAAS,GAAGjC,MAAMxB,UAAU,GAAG,MACrD;oBACA,+BAA+B;oBAE/B,+EAA+E;oBAC/E,+EAA+E;oBAC/E,4EAA4E;oBAE5E,kFAAkF;oBAClF,mFAAmF;oBACnF,+EAA+E;oBAC/E,mFAAmF;oBACnF,6EAA6E;oBAE7E,IAAIwB,OAAO;wBACT,IAAIkJ,cAAclJ,MAAMiC,SAAS,GAAGjC,MAAMvB,MAAM,GAAG,MAAM;4BACvDrB,yBAAAA,MAAQ,oBAAoB4K;wBAC9B;wBAEA,IACEtK,UAAUyL,kBAAkB,IAC5BD,cAAclJ,MAAMiC,SAAS,GAAGjC,MAAMxB,UAAU,GAAG,MACnD;4BACApB,yBAAAA,MAAQ,qCAAqC4K;wBAC/C;oBACF;oBAEA,MAAM,CAACoB,WAAWvE,kBAAkB,GAAG,MAAMpH,mBAC3CC,WACAqC,eACAnC,yBACAkK,sBACAhK,IACAC;oBAGF,gEAAgE;oBAChE,IAAI,CAACL,UAAU2L,WAAW,EAAE;wBAC1B,IAAIC;wBAEJ,IAAIrB,0BAA0B;4BAC5B,8DAA8D;4BAC9D,MAAMlD,QAAQH,uBAAuBC;4BACrCyE,kBAAkBxE,iBAAiBC,OAAO;4BAC1CkD,yBAAyBlL,KAAK,CAACwM,GAAG,CAChCvB,oBACAlD,iBAAiBC,OAAO;wBAE5B,OAAO;4BACLuE,kBAAkBzE;wBACpB;wBAEA,MAAM2E,UAAUvD,aAAasD,GAAG,CAC9BvB,oBACAsB;wBAGF5L,UAAU+L,uBAAuB,KAAK,EAAE;wBACxC/L,UAAU+L,uBAAuB,CAAClJ,IAAI,CAACiJ;oBACzC;oBAEA5F,SAASwF;gBACX,OAAO;oBACLtJ,0BAA0BC,eAAeC;oBAEzC,qDAAqD;oBACrD4D,SAAS5D,MAAMmB,KAAK;oBAEpB,qEAAqE;oBACrE,yBAAyB;oBACzB,IAAI8G,0BAA0B;wBAC5B,MAAM,CAACyB,WAAWC,WAAW,GAAGnF,gBAAgBxE;wBAChD,IAAIkC,aAAa;4BACf0B,SAASgC,4BAA4B8D,UAAUvI,KAAK,EAAEe;wBACxD,OAAO;4BACL0B,SAAS8F,UAAUvI,KAAK;wBAC1B;wBAEA8G,yBAAyBlL,KAAK,CAACwM,GAAG,CAChCvB,oBACAnF,QAAQC,OAAO,CAAC6G;oBAEpB,OAAO;wBACL,kEAAkE;wBAClE,wEAAwE;wBACxE,kCAAkC;wBAClCzH,+BAAAA,YAAaC,OAAO;oBACtB;oBAEA,IAAI+G,cAAclJ,MAAMiC,SAAS,GAAGjC,MAAMxB,UAAU,GAAG,MAAM;wBAC3D,gFAAgF;wBAChF,mEAAmE;wBACnE,MAAM,CAACoL,eAAe/E,kBAAkB,GAAG,MAAMpH,mBAC/CC,WACAF,WACAI,yBACAkK,sBACAhK,IACAC;wBAGF,IAAIuL;wBACJ,IAAIrB,0BAA0B;4BAC5B,MAAMlD,QAAQH,uBAAuBC;4BACrCyE,kBAAkBxE,iBAAiBC,OAAO;4BAC1CkD,yBAAyBlL,KAAK,CAACwM,GAAG,CAChCvB,oBACAlD,iBAAiBC,OAAO;wBAE5B,OAAO;4BACLuE,kBAAkBzE;wBACpB;wBAEA,MAAM2E,UAAUvD,aAAasD,GAAG,CAC9BvB,oBACAsB;wBAGF,IAAI,CAAC5L,UAAU+L,uBAAuB,EAAE;4BACtC/L,UAAU+L,uBAAuB,GAAG,EAAE;wBACxC;wBACA/L,UAAU+L,uBAAuB,CAAClJ,IAAI,CAACiJ;wBAEvC,MAAMI,cAAcC,MAAM;oBAC5B;gBACF;YACF;YAEA,yFAAyF;YACzF,0FAA0F;YAC1F,wFAAwF;YACxF,uFAAuF;YACvF,qFAAqF;YACrF,uFAAuF;YACvF,iFAAiF;YACjF,MAAMC,oBAAoB;YAE1B,MAAMC,yBAAyB;gBAC7B,2FAA2F;gBAC3F,yFAAyF;gBACzF,+CAA+C;gBAC/CC,eAAe;gBACfC,WAAWjN,gBACPY,wBAAwBsM,oBAAoB,GAC5CtM,wBAAwBuM,gBAAgB;gBAC5CC,iBAAiB3H,IAAAA,mCAAkB;YACrC;YAEA,OAAO4H,IAAAA,oCAAwB,EAACzG,QAAQ;gBACtCmG;gBACA1H;gBACAyH;gBACA/F,iBAAiB;YACnB;QACF;IACF,CAAC,CAACsC,KAAK;IAEP,OAAOiE,cAAK,CAACvN,KAAK,CAACuJ;AACrB;AAEA,SAASO,gBACPtE,IAAW;IAEX,IAAIA,KAAKnC,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,MAAM,CAACmK,OAAOC,IAAI,GAAGjI;IAErB,OACEiI,QAAQhN,aAAa,iDAAiD;IACtE+M,UAAU,QACV,OAAOA,UAAU,YACjB,AAACA,MAAqCE,iBAAiB;AAE3D;AAEA,SAAShL,sBACP/B,SAAoB,EACpBqC,aAAwC;IAExC,IAAIrC,UAAUgN,oBAAoB,IAAIhN,UAAU2L,WAAW,EAAE;QAC3D,OAAO;IACT;IAEA,IAAI3L,UAAUiN,GAAG,IAAI5K,eAAe;QAClC,IAAIA,cAAcnB,IAAI,KAAK,WAAW;YACpC,OAAOmB,cAAc6K,OAAO,CAACrC,GAAG,CAAC,qBAAqB;QACxD;QAEA,IAAIxI,cAAcnB,IAAI,KAAK,SAAS;YAClC,OAAOmB,cAAcP,eAAe;QACtC;IACF;IAEA,OAAO;AACT;AAEA,SAASyJ,wBACPjJ,KAAiB,EACjBtC,SAAoB,EACpBqB,YAAsB,EACtB+J,sBAA8B;IAE9B,4EAA4E;IAC5E,6CAA6C;IAC7C,IAAI9I,MAAMb,IAAI,CAAC0L,IAAI,CAAC,CAACxK,MAAQyK,yBAAyBzK,KAAK3C,aAAa;QACtE,OAAO;IACT;IAEA,sEAAsE;IACtE,gDAAgD;IAChD,IAAIsC,MAAMiC,SAAS,IAAI6G,wBAAwB;QAC7C1L,yBAAAA,MACE,wBACA4C,MAAMiC,SAAS,EACf,4CACA6G;QAGF,OAAO;IACT;IAEA,0EAA0E;IAC1E,wCAAwC;IACxC,IAAI/J,aAAa8L,IAAI,CAAC,CAACxK,MAAQyK,yBAAyBzK,KAAK3C,aAAa;QACxE,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASoN,yBAAyBzK,GAAW,EAAE3C,SAAoB;IACjE,MAAM,EAAEqN,yBAAyB,EAAEC,sBAAsB,EAAE,GAAGtN;IAE9D,4EAA4E;IAC5E,IAAIqN,0BAA0BzK,QAAQ,CAACD,MAAM;QAC3CjD,yBAAAA,MAAQ,OAAOiD,KAAK;QAEpB,OAAO;IACT;IAEA,8EAA8E;IAC9E,4EAA4E;IAC5E,sEAAsE;IACtE,IAAI2K,0CAAAA,uBAAwB1K,QAAQ,CAACD,MAAM;QACzCjD,yBAAAA,MAAQ,OAAOiD,KAAK;QAEpB,OAAO;IACT;IAEA,OAAO;AACT"}